"""
CRUD API endpoints for Store model.
"""

from ninja import Router, Schema
from ninja.errors import HttpError
from typing import Optional, List
from datetime import datetime
from django.shortcuts import get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
import logging

from stores.models import Store
from accounts.models import CustomUser
from products.models import Region
from api.middleware import AuthMiddleware

logger = logging.getLogger(__name__)

# Create router for Store endpoints
router = Router(tags=["stores"])

# ============================================================================
# SCHEMAS
# ============================================================================


class StoreIn(Schema):
    """Schema for creating a new store"""

    name: str
    description: str = ""
    address: str
    city_id: Optional[int] = None
    state_id: Optional[int] = None
    country_id: Optional[int] = None
    owner_id: int


class StoreUpdate(Schema):
    """Schema for updating store data"""

    name: Optional[str] = None
    description: Optional[str] = None
    address: Optional[str] = None
    city_id: Optional[int] = None
    state_id: Optional[int] = None
    country_id: Optional[int] = None


class CustomUserOut(Schema):
    """Simplified user schema for store responses"""

    id: int
    username: str
    phone: str


class RegionOut(Schema):
    """Simplified region schema for store responses"""

    id: int
    name: str
    type: str
    full_path: str


class StoreOut(Schema):
    """Schema for store output"""

    id: int
    name: str
    description: str
    address: str
    owner: CustomUserOut
    city: Optional[RegionOut] = None
    state: Optional[RegionOut] = None
    country: Optional[RegionOut] = None
    created_at: datetime
    updated_at: datetime


class PaginatedStoreResponse(Schema):
    """Paginated response for stores"""

    stores: List[StoreOut]
    total_count: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_previous: bool


# ============================================================================
# HELPER FUNCTIONS
# ============================================================================


def _build_store_response(store) -> StoreOut:
    """Helper function to build StoreOut response"""
    return StoreOut(
        id=store.id,
        name=store.name,
        description=store.description,
        address=store.address,
        owner=CustomUserOut(
            id=store.owner.id,
            username=store.owner.username,
            phone=store.owner.phone,
        ),
        city=RegionOut(
            id=store.city.id,
            name=store.city.name,
            type=store.city.type,
            full_path=store.city.get_full_path(),
        )
        if store.city
        else None,
        state=RegionOut(
            id=store.state.id,
            name=store.state.name,
            type=store.state.type,
            full_path=store.state.get_full_path(),
        )
        if store.state
        else None,
        country=RegionOut(
            id=store.country.id,
            name=store.country.name,
            type=store.country.type,
            full_path=store.country.get_full_path(),
        )
        if store.country
        else None,
        created_at=store.created_at,
        updated_at=store.updated_at,
    )


# ============================================================================
# CRUD ENDPOINTS
# ============================================================================


@router.get("/", response=PaginatedStoreResponse, auth=AuthMiddleware)
def list_stores(
    request,
    page: int = 1,
    page_size: int = 20,
    search: Optional[str] = None,
    owner_id: Optional[int] = None,
    city_id: Optional[int] = None,
) -> PaginatedStoreResponse:
    """
    List all stores with pagination and filtering.
    Requires authentication.
    """
    try:
        # Validate page_size
        if page_size > 100:
            page_size = 100
        elif page_size < 1:
            page_size = 20

        # Build queryset
        queryset = Store.objects.filter(deleted_at__isnull=True).select_related(
            "owner", "city", "state", "country"
        )

        # Apply filters
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search)
                | Q(description__icontains=search)
                | Q(address__icontains=search)
            )

        if owner_id:
            queryset = queryset.filter(owner_id=owner_id)

        if city_id:
            queryset = queryset.filter(city_id=city_id)

        # Order by creation date (newest first)
        queryset = queryset.order_by("-created_at")

        # Paginate
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # Convert to response format
        stores = [_build_store_response(store) for store in page_obj.object_list]

        return PaginatedStoreResponse(
            stores=stores,
            total_count=paginator.count,
            page=page,
            page_size=page_size,
            total_pages=paginator.num_pages,
            has_next=page_obj.has_next(),
            has_previous=page_obj.has_previous(),
        )

    except Exception as e:
        logger.exception(f"Error listing stores: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/my", response=PaginatedStoreResponse, auth=AuthMiddleware)
def list_my_stores(
    request,
    page: int = 1,
    page_size: int = 20,
) -> PaginatedStoreResponse:
    """
    List current user's stores with pagination.
    Requires authentication.
    """
    try:
        # Validate page_size
        if page_size > 100:
            page_size = 100
        elif page_size < 1:
            page_size = 20

        # Build queryset for current user
        queryset = Store.objects.filter(
            owner=request.user, deleted_at__isnull=True
        ).select_related("city", "state", "country")

        # Order by creation date (newest first)
        queryset = queryset.order_by("-created_at")

        # Paginate
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # Convert to response format
        stores = [_build_store_response(store) for store in page_obj.object_list]

        return PaginatedStoreResponse(
            stores=stores,
            total_count=paginator.count,
            page=page,
            page_size=page_size,
            total_pages=paginator.num_pages,
            has_next=page_obj.has_next(),
            has_previous=page_obj.has_previous(),
        )

    except Exception as e:
        logger.exception(f"Error listing user stores: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/{store_id}", response=StoreOut, auth=AuthMiddleware)
def get_store(request, store_id: int) -> StoreOut:
    """
    Get a specific store by ID.
    Requires authentication.
    """
    try:
        store = get_object_or_404(
            Store.objects.select_related("owner", "city", "state", "country"),
            id=store_id,
            deleted_at__isnull=True,
        )

        return _build_store_response(store)

    except Exception as e:
        logger.exception(f"Error getting store {store_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.post("/", response=StoreOut, auth=AuthMiddleware)
def create_store(request, data: StoreIn) -> StoreOut:
    """
    Create a new store.
    Requires authentication.
    """
    try:
        # Check if owner exists
        owner = get_object_or_404(CustomUser, id=data.owner_id, deleted_at__isnull=True)

        # Validate regions if provided
        city = None
        if data.city_id:
            city = get_object_or_404(Region, id=data.city_id, deleted_at__isnull=True)

        state = None
        if data.state_id:
            state = get_object_or_404(Region, id=data.state_id, deleted_at__isnull=True)

        country = None
        if data.country_id:
            country = get_object_or_404(
                Region, id=data.country_id, deleted_at__isnull=True
            )

        # Create store
        store = Store.objects.create(
            name=data.name,
            description=data.description,
            address=data.address,
            owner=owner,
            city=city,
            state=state,
            country=country,
        )

        return _build_store_response(store)

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error creating store: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.put("/{store_id}", response=StoreOut, auth=AuthMiddleware)
def update_store(request, store_id: int, data: StoreUpdate) -> StoreOut:
    """
    Update a store.
    Requires authentication.
    """
    try:
        store = get_object_or_404(
            Store.objects.select_related("owner", "city", "state", "country"),
            id=store_id,
            deleted_at__isnull=True,
        )

        # Update fields if provided
        if data.name is not None:
            store.name = data.name

        if data.description is not None:
            store.description = data.description

        if data.address is not None:
            store.address = data.address

        if data.city_id is not None:
            if data.city_id == 0:
                store.city = None
            else:
                city = get_object_or_404(
                    Region, id=data.city_id, deleted_at__isnull=True
                )
                store.city = city

        if data.state_id is not None:
            if data.state_id == 0:
                store.state = None
            else:
                state = get_object_or_404(
                    Region, id=data.state_id, deleted_at__isnull=True
                )
                store.state = state

        if data.country_id is not None:
            if data.country_id == 0:
                store.country = None
            else:
                country = get_object_or_404(
                    Region, id=data.country_id, deleted_at__isnull=True
                )
                store.country = country

        store.save()

        return _build_store_response(store)

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error updating store {store_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.delete("/{store_id}", auth=AuthMiddleware)
def delete_store(request, store_id: int):
    """
    Soft delete a store.
    Requires authentication.
    """
    try:
        store = get_object_or_404(Store, id=store_id, deleted_at__isnull=True)

        # Check if store has orders
        if store.orders.filter(deleted_at__isnull=True).exists():
            raise HttpError(400, "Cannot delete store that has orders")

        # Perform soft delete
        store.delete()

        return {"message": "Store deleted successfully"}

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error deleting store {store_id}: {str(e)}")
        raise HttpError(500, "Internal server error")
