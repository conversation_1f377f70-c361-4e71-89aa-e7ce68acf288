"""
CRUD API endpoints for Region model.
"""

from ninja import Router, Schema
from ninja.errors import HttpError
from typing import Optional, List
from datetime import datetime
from django.shortcuts import get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
import logging

from products.models import Region, RegionType
from api.middleware import AuthMiddleware

logger = logging.getLogger(__name__)

# Create router for Region endpoints
router = Router(tags=["regions"])

# ============================================================================
# SCHEMAS
# ============================================================================


class RegionIn(Schema):
    """Schema for creating a new region"""

    name: str
    type: str
    parent_id: Optional[int] = None
    code: Optional[str] = None
    slug: str
    is_active: bool = True


class RegionUpdate(Schema):
    """Schema for updating region data"""

    name: Optional[str] = None
    type: Optional[str] = None
    parent_id: Optional[int] = None
    code: Optional[str] = None
    slug: Optional[str] = None
    is_active: Optional[bool] = None


class RegionOut(Schema):
    """Schema for region output"""

    id: int
    name: str
    type: str
    parent_id: Optional[int] = None
    code: Optional[str] = None
    slug: str
    is_active: bool
    full_path: str
    created_at: datetime
    updated_at: datetime


class RegionWithHierarchyOut(Schema):
    """Schema for region output with hierarchy information"""

    id: int
    name: str
    type: str
    parent_id: Optional[int] = None
    code: Optional[str] = None
    slug: str
    is_active: bool
    full_path: str
    hierarchical_name: str
    created_at: datetime
    updated_at: datetime


class PaginatedRegionResponse(Schema):
    """Paginated response for regions"""

    regions: List[RegionOut]
    total_count: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_previous: bool


# ============================================================================
# CRUD ENDPOINTS
# ============================================================================


@router.get("/", response=PaginatedRegionResponse, auth=AuthMiddleware)
def list_regions(
    request,
    page: int = 1,
    page_size: int = 20,
    search: Optional[str] = None,
    type: Optional[str] = None,
    parent_id: Optional[int] = None,
    is_active: Optional[bool] = None,
) -> PaginatedRegionResponse:
    """
    List all regions with pagination and filtering.
    Requires authentication.
    """
    try:
        # Validate page_size
        if page_size > 100:
            page_size = 100
        elif page_size < 1:
            page_size = 20

        # Build queryset
        queryset = Region.objects.filter(deleted_at__isnull=True).select_related(
            "parent"
        )

        # Apply filters
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search)
                | Q(code__icontains=search)
                | Q(slug__icontains=search)
            )

        if type:
            queryset = queryset.filter(type=type)

        if parent_id is not None:
            if parent_id == 0:
                queryset = queryset.filter(parent__isnull=True)
            else:
                queryset = queryset.filter(parent_id=parent_id)

        if is_active is not None:
            queryset = queryset.filter(is_active=is_active)

        # Order by type and name
        queryset = queryset.order_by("type", "name")

        # Paginate
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # Convert to response format
        regions = []
        for region in page_obj.object_list:
            regions.append(
                RegionOut(
                    id=region.id,
                    name=region.name,
                    type=region.type,
                    parent_id=region.parent.id if region.parent else None,
                    code=region.code,
                    slug=region.slug,
                    is_active=region.is_active,
                    full_path=region.get_full_path(),
                    created_at=region.created_at,
                    updated_at=region.updated_at,
                )
            )

        return PaginatedRegionResponse(
            regions=regions,
            total_count=paginator.count,
            page=page,
            page_size=page_size,
            total_pages=paginator.num_pages,
            has_next=page_obj.has_next(),
            has_previous=page_obj.has_previous(),
        )

    except Exception as e:
        logger.exception(f"Error listing regions: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/hierarchy", response=List[RegionWithHierarchyOut], auth=AuthMiddleware)
def list_regions_with_hierarchy(request) -> List[RegionWithHierarchyOut]:
    """
    List all regions with hierarchical names.
    Requires authentication.
    """
    try:
        regions = Region.objects.filter(deleted_at__isnull=True).select_related(
            "parent"
        )

        result = []
        for region in regions:
            # Build hierarchical name (child - parent - grandparent)
            hierarchical_name = region.name
            current = region

            # Traverse up the parent hierarchy
            while current.parent:
                current = current.parent
                hierarchical_name = f"{hierarchical_name} - {current.name}"

            result.append(
                RegionWithHierarchyOut(
                    id=region.id,
                    name=region.name,
                    type=region.type,
                    parent_id=region.parent.id if region.parent else None,
                    code=region.code,
                    slug=region.slug,
                    is_active=region.is_active,
                    full_path=region.get_full_path(),
                    hierarchical_name=hierarchical_name,
                    created_at=region.created_at,
                    updated_at=region.updated_at,
                )
            )

        return result

    except Exception as e:
        logger.exception(f"Error listing regions with hierarchy: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/countries", response=List[RegionWithHierarchyOut], auth=AuthMiddleware)
def get_countries(request) -> List[RegionWithHierarchyOut]:
    """
    List all countries.
    Requires authentication.
    """
    try:
        regions = Region.objects.filter(
            deleted_at__isnull=True, parent__isnull=True, type=RegionType.COUNTRY
        )

        result = []
        for region in regions:
            result.append(
                RegionWithHierarchyOut(
                    id=region.id,
                    name=region.name,
                    type=region.type,
                    parent_id=None,
                    code=region.code,
                    slug=region.slug,
                    is_active=region.is_active,
                    full_path=region.get_full_path(),
                    hierarchical_name=region.name,
                    created_at=region.created_at,
                    updated_at=region.updated_at,
                )
            )

        return result

    except Exception as e:
        logger.exception(f"Error getting countries: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/states", response=List[RegionWithHierarchyOut], auth=AuthMiddleware)
def get_states(
    request, country_id: Optional[int] = None
) -> List[RegionWithHierarchyOut]:
    """
    List all states/provinces, optionally filtered by country.
    Requires authentication.
    """
    try:
        queryset = Region.objects.filter(
            deleted_at__isnull=True, type=RegionType.STATE
        ).select_related("parent")

        if country_id:
            queryset = queryset.filter(parent_id=country_id)

        result = []
        for region in queryset:
            # Build hierarchical name
            hierarchical_name = region.name
            if region.parent:
                hierarchical_name = f"{hierarchical_name} - {region.parent.name}"

            result.append(
                RegionWithHierarchyOut(
                    id=region.id,
                    name=region.name,
                    type=region.type,
                    parent_id=region.parent.id if region.parent else None,
                    code=region.code,
                    slug=region.slug,
                    is_active=region.is_active,
                    full_path=region.get_full_path(),
                    hierarchical_name=hierarchical_name,
                    created_at=region.created_at,
                    updated_at=region.updated_at,
                )
            )

        return result

    except Exception as e:
        logger.exception(f"Error getting states: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/cities", response=List[RegionWithHierarchyOut], auth=AuthMiddleware)
def get_cities(request, state_id: Optional[int] = None) -> List[RegionWithHierarchyOut]:
    """
    List all cities, optionally filtered by state.
    Requires authentication.
    """
    try:
        queryset = Region.objects.filter(
            deleted_at__isnull=True, type=RegionType.CITY
        ).select_related("parent")

        if state_id:
            queryset = queryset.filter(parent_id=state_id)

        result = []
        for region in queryset:
            # Build hierarchical name
            hierarchical_name = region.name
            current = region
            while current.parent:
                current = current.parent
                hierarchical_name = f"{hierarchical_name} - {current.name}"

            result.append(
                RegionWithHierarchyOut(
                    id=region.id,
                    name=region.name,
                    type=region.type,
                    parent_id=region.parent.id if region.parent else None,
                    code=region.code,
                    slug=region.slug,
                    is_active=region.is_active,
                    full_path=region.get_full_path(),
                    hierarchical_name=hierarchical_name,
                    created_at=region.created_at,
                    updated_at=region.updated_at,
                )
            )

        return result

    except Exception as e:
        logger.exception(f"Error getting cities: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/{region_id}", response=RegionOut, auth=AuthMiddleware)
def get_region(request, region_id: int) -> RegionOut:
    """
    Get a specific region by ID.
    Requires authentication.
    """
    try:
        region = get_object_or_404(
            Region.objects.select_related("parent"),
            id=region_id,
            deleted_at__isnull=True,
        )

        return RegionOut(
            id=region.id,
            name=region.name,
            type=region.type,
            parent_id=region.parent.id if region.parent else None,
            code=region.code,
            slug=region.slug,
            is_active=region.is_active,
            full_path=region.get_full_path(),
            created_at=region.created_at,
            updated_at=region.updated_at,
        )

    except Exception as e:
        logger.exception(f"Error getting region {region_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.post("/", response=RegionOut, auth=AuthMiddleware)
def create_region(request, data: RegionIn) -> RegionOut:
    """
    Create a new region.
    Requires authentication.
    """
    try:
        # Validate region type
        if data.type not in [choice.value for choice in RegionType]:
            raise HttpError(
                400,
                f"Invalid region type. Must be one of: {[choice.value for choice in RegionType]}",
            )

        # Check if slug already exists
        if Region.objects.filter(slug=data.slug).exists():
            raise HttpError(400, "Region with this slug already exists")

        # Validate parent if provided
        parent = None
        if data.parent_id:
            parent = get_object_or_404(
                Region, id=data.parent_id, deleted_at__isnull=True
            )

        # Create region
        region = Region.objects.create(
            name=data.name,
            type=data.type,
            parent=parent,
            code=data.code,
            slug=data.slug,
            is_active=data.is_active,
        )

        return RegionOut(
            id=region.id,
            name=region.name,
            type=region.type,
            parent_id=region.parent.id if region.parent else None,
            code=region.code,
            slug=region.slug,
            is_active=region.is_active,
            full_path=region.get_full_path(),
            created_at=region.created_at,
            updated_at=region.updated_at,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error creating region: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.put("/{region_id}", response=RegionOut, auth=AuthMiddleware)
def update_region(request, region_id: int, data: RegionUpdate) -> RegionOut:
    """
    Update a region.
    Requires authentication.
    """
    try:
        region = get_object_or_404(
            Region.objects.select_related("parent"),
            id=region_id,
            deleted_at__isnull=True,
        )

        # Update fields if provided
        if data.name is not None:
            region.name = data.name

        if data.type is not None:
            if data.type not in [choice.value for choice in RegionType]:
                raise HttpError(
                    400,
                    f"Invalid region type. Must be one of: {[choice.value for choice in RegionType]}",
                )
            region.type = data.type

        if data.parent_id is not None:
            if data.parent_id == 0:
                region.parent = None
            else:
                if data.parent_id == region_id:
                    raise HttpError(400, "Region cannot be its own parent")
                parent = get_object_or_404(
                    Region, id=data.parent_id, deleted_at__isnull=True
                )
                region.parent = parent

        if data.code is not None:
            region.code = data.code

        if data.slug is not None:
            if Region.objects.filter(slug=data.slug).exclude(id=region_id).exists():
                raise HttpError(400, "Region with this slug already exists")
            region.slug = data.slug

        if data.is_active is not None:
            region.is_active = data.is_active

        region.save()

        return RegionOut(
            id=region.id,
            name=region.name,
            type=region.type,
            parent_id=region.parent.id if region.parent else None,
            code=region.code,
            slug=region.slug,
            is_active=region.is_active,
            full_path=region.get_full_path(),
            created_at=region.created_at,
            updated_at=region.updated_at,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error updating region {region_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.delete("/{region_id}", auth=AuthMiddleware)
def delete_region(request, region_id: int):
    """
    Soft delete a region.
    Requires authentication.
    """
    try:
        region = get_object_or_404(Region, id=region_id, deleted_at__isnull=True)

        # Check if region has children
        if region.children.filter(deleted_at__isnull=True).exists():
            raise HttpError(400, "Cannot delete region that has child regions")

        # Check if region has stores
        if (
            hasattr(region, "stores_city")
            and region.stores_city.filter(deleted_at__isnull=True).exists()
        ):
            raise HttpError(400, "Cannot delete region that has stores")

        # Perform soft delete
        region.delete()

        return {"message": "Region deleted successfully"}

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error deleting region {region_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/{region_id}/children", response=List[RegionOut], auth=AuthMiddleware)
def get_region_children(request, region_id: int) -> List[RegionOut]:
    """
    Get all child regions of a specific region.
    Requires authentication.
    """
    try:
        region = get_object_or_404(Region, id=region_id, deleted_at__isnull=True)

        children = region.children.filter(deleted_at__isnull=True).order_by("name")

        result = []
        for child in children:
            result.append(
                RegionOut(
                    id=child.id,
                    name=child.name,
                    type=child.type,
                    parent_id=child.parent.id if child.parent else None,
                    code=child.code,
                    slug=child.slug,
                    is_active=child.is_active,
                    full_path=child.get_full_path(),
                    created_at=child.created_at,
                    updated_at=child.updated_at,
                )
            )

        return result

    except Exception as e:
        logger.exception(f"Error getting region children {region_id}: {str(e)}")
        raise HttpError(500, "Internal server error")
