"""
CRUD API endpoints for Company model.
"""

from ninja import Router, Schema
from ninja.errors import HttpError
from typing import Optional, List
from datetime import datetime
from django.shortcuts import get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
import logging

from products.models import Company
from api.middleware import AuthMiddleware

logger = logging.getLogger(__name__)

# Create router for Company endpoints
router = Router(tags=["companies"])

# ============================================================================
# SCHEMAS
# ============================================================================

class CompanyIn(Schema):
    """Schema for creating a new company"""
    name: str
    title: str
    slug: str


class CompanyUpdate(Schema):
    """Schema for updating company data"""
    name: Optional[str] = None
    title: Optional[str] = None
    slug: Optional[str] = None


class CompanyOut(Schema):
    """Schema for company output"""
    id: int
    name: str
    title: str
    slug: str
    created_at: datetime
    updated_at: datetime


class PaginatedCompanyResponse(Schema):
    """Paginated response for companies"""
    companies: List[CompanyOut]
    total_count: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_previous: bool


# ============================================================================
# CRUD ENDPOINTS
# ============================================================================

@router.get("/", response=PaginatedCompanyResponse, auth=AuthMiddleware)
def list_companies(
    request,
    page: int = 1,
    page_size: int = 20,
    search: Optional[str] = None,
) -> PaginatedCompanyResponse:
    """
    List all companies with pagination and filtering.
    Requires authentication.
    """
    try:
        # Validate page_size
        if page_size > 100:
            page_size = 100
        elif page_size < 1:
            page_size = 20

        # Build queryset
        queryset = Company.objects.filter(deleted_at__isnull=True)

        # Apply search
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(title__icontains=search) |
                Q(slug__icontains=search)
            )

        # Order by name
        queryset = queryset.order_by('name')

        # Paginate
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # Convert to response format
        companies = []
        for company in page_obj.object_list:
            companies.append(CompanyOut(
                id=company.id,
                name=company.name,
                title=company.title,
                slug=company.slug,
                created_at=company.created_at,
                updated_at=company.updated_at,
            ))

        return PaginatedCompanyResponse(
            companies=companies,
            total_count=paginator.count,
            page=page,
            page_size=page_size,
            total_pages=paginator.num_pages,
            has_next=page_obj.has_next(),
            has_previous=page_obj.has_previous(),
        )

    except Exception as e:
        logger.exception(f"Error listing companies: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/{company_id}", response=CompanyOut, auth=AuthMiddleware)
def get_company(request, company_id: int) -> CompanyOut:
    """
    Get a specific company by ID.
    Requires authentication.
    """
    try:
        company = get_object_or_404(
            Company,
            id=company_id,
            deleted_at__isnull=True
        )

        return CompanyOut(
            id=company.id,
            name=company.name,
            title=company.title,
            slug=company.slug,
            created_at=company.created_at,
            updated_at=company.updated_at,
        )

    except Exception as e:
        logger.exception(f"Error getting company {company_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.post("/", response=CompanyOut, auth=AuthMiddleware)
def create_company(request, data: CompanyIn) -> CompanyOut:
    """
    Create a new company.
    Requires authentication.
    """
    try:
        # Check if slug already exists
        if Company.objects.filter(slug=data.slug).exists():
            raise HttpError(400, "Company with this slug already exists")

        # Create company
        company = Company.objects.create(
            name=data.name,
            title=data.title,
            slug=data.slug,
        )

        return CompanyOut(
            id=company.id,
            name=company.name,
            title=company.title,
            slug=company.slug,
            created_at=company.created_at,
            updated_at=company.updated_at,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error creating company: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.put("/{company_id}", response=CompanyOut, auth=AuthMiddleware)
def update_company(request, company_id: int, data: CompanyUpdate) -> CompanyOut:
    """
    Update a company.
    Requires authentication.
    """
    try:
        company = get_object_or_404(
            Company,
            id=company_id,
            deleted_at__isnull=True
        )

        # Update fields if provided
        if data.name is not None:
            company.name = data.name

        if data.title is not None:
            company.title = data.title

        if data.slug is not None:
            if Company.objects.filter(slug=data.slug).exclude(id=company_id).exists():
                raise HttpError(400, "Company with this slug already exists")
            company.slug = data.slug

        company.save()

        return CompanyOut(
            id=company.id,
            name=company.name,
            title=company.title,
            slug=company.slug,
            created_at=company.created_at,
            updated_at=company.updated_at,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error updating company {company_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.delete("/{company_id}", auth=AuthMiddleware)
def delete_company(request, company_id: int):
    """
    Soft delete a company.
    Requires authentication.
    """
    try:
        company = get_object_or_404(
            Company,
            id=company_id,
            deleted_at__isnull=True
        )

        # Check if company has products
        if company.products.filter(deleted_at__isnull=True).exists():
            raise HttpError(400, "Cannot delete company that has products")

        # Perform soft delete
        company.delete()

        return {"message": "Company deleted successfully"}

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error deleting company {company_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/{company_id}/products", auth=AuthMiddleware)
def get_company_products(request, company_id: int):
    """
    Get all products from a company.
    Requires authentication.
    """
    try:
        company = get_object_or_404(
            Company,
            id=company_id,
            deleted_at__isnull=True
        )

        products = company.products.filter(deleted_at__isnull=True).select_related('category')
        
        products_data = []
        for product in products:
            products_data.append({
                "id": product.id,
                "name": product.name,
                "title": product.title,
                "barcode": product.barcode,
                "slug": product.slug,
                "image_url": product.image.url if product.image else None,
                "category": {
                    "id": product.category.id,
                    "name": product.category.name,
                    "title": product.category.title,
                    "slug": product.category.slug,
                } if product.category else None,
                "unit": product.unit,
                "unit_count": product.unit_count,
            })

        return {
            "company": CompanyOut(
                id=company.id,
                name=company.name,
                title=company.title,
                slug=company.slug,
                created_at=company.created_at,
                updated_at=company.updated_at,
            ),
            "products": products_data,
            "products_count": len(products_data)
        }

    except Exception as e:
        logger.exception(f"Error getting company products {company_id}: {str(e)}")
        raise HttpError(500, "Internal server error")
