"""
CRUD API endpoints for Wallet model.
"""

from ninja import Router, Schema
from ninja.errors import HttpError
from typing import Optional, List
from datetime import datetime
from decimal import Decimal
from django.shortcuts import get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
import logging

from accounts.models import Wallet, CustomUser
from api.middleware import AuthMiddleware

logger = logging.getLogger(__name__)

# Create router for Wallet endpoints
router = Router(tags=["wallets"])

# ============================================================================
# SCHEMAS
# ============================================================================

class WalletIn(Schema):
    """Schema for creating a new wallet"""
    user_id: int
    balance: Decimal = Decimal('0.00')


class WalletUpdate(Schema):
    """Schema for updating wallet data"""
    balance: Optional[Decimal] = None


class CustomUserOut(Schema):
    """Simplified user schema for wallet responses"""
    id: int
    username: str
    phone: str
    email: Optional[str] = None


class WalletOut(Schema):
    """Schema for wallet output"""
    id: int
    user: CustomUserOut
    balance: Decimal
    created_at: datetime
    updated_at: datetime


class PaginatedWalletResponse(Schema):
    """Paginated response for wallets"""
    wallets: List[WalletOut]
    total_count: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_previous: bool


# ============================================================================
# CRUD ENDPOINTS
# ============================================================================

@router.get("/", response=PaginatedWalletResponse, auth=AuthMiddleware)
def list_wallets(
    request,
    page: int = 1,
    page_size: int = 20,
    user_id: Optional[int] = None,
    min_balance: Optional[Decimal] = None,
    max_balance: Optional[Decimal] = None,
) -> PaginatedWalletResponse:
    """
    List all wallets with pagination and filtering.
    Requires authentication.
    """
    try:
        # Validate page_size
        if page_size > 100:
            page_size = 100
        elif page_size < 1:
            page_size = 20

        # Build queryset
        queryset = Wallet.objects.filter(deleted_at__isnull=True).select_related('user')

        # Apply filters
        if user_id:
            queryset = queryset.filter(user_id=user_id)

        if min_balance is not None:
            queryset = queryset.filter(balance__gte=min_balance)

        if max_balance is not None:
            queryset = queryset.filter(balance__lte=max_balance)

        # Order by creation date
        queryset = queryset.order_by('-created_at')

        # Paginate
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # Convert to response format
        wallets = []
        for wallet in page_obj.object_list:
            wallets.append(WalletOut(
                id=wallet.id,
                user=CustomUserOut(
                    id=wallet.user.id,
                    username=wallet.user.username,
                    phone=wallet.user.phone,
                    email=wallet.user.email,
                ),
                balance=wallet.balance,
                created_at=wallet.created_at,
                updated_at=wallet.updated_at,
            ))

        return PaginatedWalletResponse(
            wallets=wallets,
            total_count=paginator.count,
            page=page,
            page_size=page_size,
            total_pages=paginator.num_pages,
            has_next=page_obj.has_next(),
            has_previous=page_obj.has_previous(),
        )

    except Exception as e:
        logger.exception(f"Error listing wallets: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/{wallet_id}", response=WalletOut, auth=AuthMiddleware)
def get_wallet(request, wallet_id: int) -> WalletOut:
    """
    Get a specific wallet by ID.
    Requires authentication.
    """
    try:
        wallet = get_object_or_404(
            Wallet.objects.select_related('user'),
            id=wallet_id,
            deleted_at__isnull=True
        )

        return WalletOut(
            id=wallet.id,
            user=CustomUserOut(
                id=wallet.user.id,
                username=wallet.user.username,
                phone=wallet.user.phone,
                email=wallet.user.email,
            ),
            balance=wallet.balance,
            created_at=wallet.created_at,
            updated_at=wallet.updated_at,
        )

    except Exception as e:
        logger.exception(f"Error getting wallet {wallet_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/user/{user_id}", response=WalletOut, auth=AuthMiddleware)
def get_user_wallet(request, user_id: int) -> WalletOut:
    """
    Get a user's wallet.
    Requires authentication.
    """
    try:
        wallet = get_object_or_404(
            Wallet.objects.select_related('user'),
            user_id=user_id,
            deleted_at__isnull=True
        )

        return WalletOut(
            id=wallet.id,
            user=CustomUserOut(
                id=wallet.user.id,
                username=wallet.user.username,
                phone=wallet.user.phone,
                email=wallet.user.email,
            ),
            balance=wallet.balance,
            created_at=wallet.created_at,
            updated_at=wallet.updated_at,
        )

    except Exception as e:
        logger.exception(f"Error getting wallet for user {user_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.post("/", response=WalletOut, auth=AuthMiddleware)
def create_wallet(request, data: WalletIn) -> WalletOut:
    """
    Create a new wallet.
    Requires authentication.
    """
    try:
        # Check if user exists
        user = get_object_or_404(
            CustomUser,
            id=data.user_id,
            deleted_at__isnull=True
        )

        # Check if user already has a wallet
        if Wallet.objects.filter(user=user, deleted_at__isnull=True).exists():
            raise HttpError(400, "User already has a wallet")

        # Create wallet
        wallet = Wallet.objects.create(
            user=user,
            balance=data.balance,
        )

        return WalletOut(
            id=wallet.id,
            user=CustomUserOut(
                id=wallet.user.id,
                username=wallet.user.username,
                phone=wallet.user.phone,
                email=wallet.user.email,
            ),
            balance=wallet.balance,
            created_at=wallet.created_at,
            updated_at=wallet.updated_at,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error creating wallet: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.put("/{wallet_id}", response=WalletOut, auth=AuthMiddleware)
def update_wallet(request, wallet_id: int, data: WalletUpdate) -> WalletOut:
    """
    Update a wallet.
    Requires authentication.
    """
    try:
        wallet = get_object_or_404(
            Wallet.objects.select_related('user'),
            id=wallet_id,
            deleted_at__isnull=True
        )

        # Update fields if provided
        if data.balance is not None:
            if data.balance < 0:
                raise HttpError(400, "Balance cannot be negative")
            wallet.balance = data.balance

        wallet.save()

        return WalletOut(
            id=wallet.id,
            user=CustomUserOut(
                id=wallet.user.id,
                username=wallet.user.username,
                phone=wallet.user.phone,
                email=wallet.user.email,
            ),
            balance=wallet.balance,
            created_at=wallet.created_at,
            updated_at=wallet.updated_at,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error updating wallet {wallet_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.delete("/{wallet_id}", auth=AuthMiddleware)
def delete_wallet(request, wallet_id: int):
    """
    Soft delete a wallet.
    Requires authentication.
    """
    try:
        wallet = get_object_or_404(
            Wallet,
            id=wallet_id,
            deleted_at__isnull=True
        )

        # Perform soft delete
        wallet.delete()

        return {"message": "Wallet deleted successfully"}

    except Exception as e:
        logger.exception(f"Error deleting wallet {wallet_id}: {str(e)}")
        raise HttpError(500, "Internal server error")
