"""
CRUD API endpoints for Transaction model.
"""

from ninja import Router, Schema
from ninja.errors import HttpError
from typing import Optional, List
from datetime import datetime
from decimal import Decimal
from django.shortcuts import get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
import logging

from accounts.models import Transaction, TransactionActionEnum, Wallet, CustomUser
from stores.models import Order
from api.middleware import AuthMiddleware

logger = logging.getLogger(__name__)

# Create router for Transaction endpoints
router = Router(tags=["transactions"])

# ============================================================================
# SCHEMAS
# ============================================================================

class TransactionIn(Schema):
    """Schema for creating a new transaction"""
    user_id: int
    wallet_id: int
    action: str
    amount: Decimal
    related_order_id: Optional[int] = None


class TransactionUpdate(Schema):
    """Schema for updating transaction data"""
    action: Optional[str] = None
    amount: Optional[Decimal] = None


class CustomUserOut(Schema):
    """Simplified user schema for transaction responses"""
    id: int
    username: str
    phone: str


class WalletOut(Schema):
    """Simplified wallet schema for transaction responses"""
    id: int
    balance: Decimal


class OrderOut(Schema):
    """Simplified order schema for transaction responses"""
    id: int
    total_price: Decimal
    status: str


class TransactionOut(Schema):
    """Schema for transaction output"""
    id: int
    user: CustomUserOut
    wallet: WalletOut
    related_order: Optional[OrderOut] = None
    action: str
    amount: Decimal
    created_at: datetime
    updated_at: datetime


class PaginatedTransactionResponse(Schema):
    """Paginated response for transactions"""
    transactions: List[TransactionOut]
    total_count: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_previous: bool


# ============================================================================
# CRUD ENDPOINTS
# ============================================================================

@router.get("/", response=PaginatedTransactionResponse, auth=AuthMiddleware)
def list_transactions(
    request,
    page: int = 1,
    page_size: int = 20,
    user_id: Optional[int] = None,
    wallet_id: Optional[int] = None,
    action: Optional[str] = None,
    min_amount: Optional[Decimal] = None,
    max_amount: Optional[Decimal] = None,
) -> PaginatedTransactionResponse:
    """
    List all transactions with pagination and filtering.
    Requires authentication.
    """
    try:
        # Validate page_size
        if page_size > 100:
            page_size = 100
        elif page_size < 1:
            page_size = 20

        # Build queryset
        queryset = Transaction.objects.filter(
            deleted_at__isnull=True
        ).select_related('user', 'wallet', 'related_order')

        # Apply filters
        if user_id:
            queryset = queryset.filter(user_id=user_id)

        if wallet_id:
            queryset = queryset.filter(wallet_id=wallet_id)

        if action:
            queryset = queryset.filter(action=action)

        if min_amount is not None:
            queryset = queryset.filter(amount__gte=min_amount)

        if max_amount is not None:
            queryset = queryset.filter(amount__lte=max_amount)

        # Order by creation date (newest first)
        queryset = queryset.order_by('-created_at')

        # Paginate
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # Convert to response format
        transactions = []
        for transaction in page_obj.object_list:
            transactions.append(TransactionOut(
                id=transaction.id,
                user=CustomUserOut(
                    id=transaction.user.id,
                    username=transaction.user.username,
                    phone=transaction.user.phone,
                ),
                wallet=WalletOut(
                    id=transaction.wallet.id,
                    balance=transaction.wallet.balance,
                ),
                related_order=OrderOut(
                    id=transaction.related_order.id,
                    total_price=transaction.related_order.total_price,
                    status=transaction.related_order.status,
                ) if transaction.related_order else None,
                action=transaction.action,
                amount=transaction.amount,
                created_at=transaction.created_at,
                updated_at=transaction.updated_at,
            ))

        return PaginatedTransactionResponse(
            transactions=transactions,
            total_count=paginator.count,
            page=page,
            page_size=page_size,
            total_pages=paginator.num_pages,
            has_next=page_obj.has_next(),
            has_previous=page_obj.has_previous(),
        )

    except Exception as e:
        logger.exception(f"Error listing transactions: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/{transaction_id}", response=TransactionOut, auth=AuthMiddleware)
def get_transaction(request, transaction_id: int) -> TransactionOut:
    """
    Get a specific transaction by ID.
    Requires authentication.
    """
    try:
        transaction = get_object_or_404(
            Transaction.objects.select_related('user', 'wallet', 'related_order'),
            id=transaction_id,
            deleted_at__isnull=True
        )

        return TransactionOut(
            id=transaction.id,
            user=CustomUserOut(
                id=transaction.user.id,
                username=transaction.user.username,
                phone=transaction.user.phone,
            ),
            wallet=WalletOut(
                id=transaction.wallet.id,
                balance=transaction.wallet.balance,
            ),
            related_order=OrderOut(
                id=transaction.related_order.id,
                total_price=transaction.related_order.total_price,
                status=transaction.related_order.status,
            ) if transaction.related_order else None,
            action=transaction.action,
            amount=transaction.amount,
            created_at=transaction.created_at,
            updated_at=transaction.updated_at,
        )

    except Exception as e:
        logger.exception(f"Error getting transaction {transaction_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.post("/", response=TransactionOut, auth=AuthMiddleware)
def create_transaction(request, data: TransactionIn) -> TransactionOut:
    """
    Create a new transaction.
    Requires authentication.
    """
    try:
        # Validate action
        if data.action not in [choice.value for choice in TransactionActionEnum]:
            raise HttpError(400, f"Invalid action. Must be one of: {[choice.value for choice in TransactionActionEnum]}")

        # Check if user exists
        user = get_object_or_404(
            CustomUser,
            id=data.user_id,
            deleted_at__isnull=True
        )

        # Check if wallet exists and belongs to user
        wallet = get_object_or_404(
            Wallet,
            id=data.wallet_id,
            user=user,
            deleted_at__isnull=True
        )

        # Check if related order exists (if provided)
        related_order = None
        if data.related_order_id:
            related_order = get_object_or_404(
                Order,
                id=data.related_order_id,
                deleted_at__isnull=True
            )

        # Validate amount
        if data.amount <= 0:
            raise HttpError(400, "Amount must be positive")

        # Create transaction
        transaction = Transaction.objects.create(
            user=user,
            wallet=wallet,
            related_order=related_order,
            action=data.action,
            amount=data.amount,
        )

        return TransactionOut(
            id=transaction.id,
            user=CustomUserOut(
                id=transaction.user.id,
                username=transaction.user.username,
                phone=transaction.user.phone,
            ),
            wallet=WalletOut(
                id=transaction.wallet.id,
                balance=transaction.wallet.balance,
            ),
            related_order=OrderOut(
                id=transaction.related_order.id,
                total_price=transaction.related_order.total_price,
                status=transaction.related_order.status,
            ) if transaction.related_order else None,
            action=transaction.action,
            amount=transaction.amount,
            created_at=transaction.created_at,
            updated_at=transaction.updated_at,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error creating transaction: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.put("/{transaction_id}", response=TransactionOut, auth=AuthMiddleware)
def update_transaction(request, transaction_id: int, data: TransactionUpdate) -> TransactionOut:
    """
    Update a transaction.
    Requires authentication.
    """
    try:
        transaction = get_object_or_404(
            Transaction.objects.select_related('user', 'wallet', 'related_order'),
            id=transaction_id,
            deleted_at__isnull=True
        )

        # Update fields if provided
        if data.action is not None:
            if data.action not in [choice.value for choice in TransactionActionEnum]:
                raise HttpError(400, f"Invalid action. Must be one of: {[choice.value for choice in TransactionActionEnum]}")
            transaction.action = data.action

        if data.amount is not None:
            if data.amount <= 0:
                raise HttpError(400, "Amount must be positive")
            transaction.amount = data.amount

        transaction.save()

        return TransactionOut(
            id=transaction.id,
            user=CustomUserOut(
                id=transaction.user.id,
                username=transaction.user.username,
                phone=transaction.user.phone,
            ),
            wallet=WalletOut(
                id=transaction.wallet.id,
                balance=transaction.wallet.balance,
            ),
            related_order=OrderOut(
                id=transaction.related_order.id,
                total_price=transaction.related_order.total_price,
                status=transaction.related_order.status,
            ) if transaction.related_order else None,
            action=transaction.action,
            amount=transaction.amount,
            created_at=transaction.created_at,
            updated_at=transaction.updated_at,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error updating transaction {transaction_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.delete("/{transaction_id}", auth=AuthMiddleware)
def delete_transaction(request, transaction_id: int):
    """
    Soft delete a transaction.
    Requires authentication.
    """
    try:
        transaction = get_object_or_404(
            Transaction,
            id=transaction_id,
            deleted_at__isnull=True
        )

        # Perform soft delete
        transaction.delete()

        return {"message": "Transaction deleted successfully"}

    except Exception as e:
        logger.exception(f"Error deleting transaction {transaction_id}: {str(e)}")
        raise HttpError(500, "Internal server error")
