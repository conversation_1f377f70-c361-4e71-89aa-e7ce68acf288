"""
CRUD API endpoints for Notification model.
"""

from ninja import Router, Schema
from ninja.errors import HttpError
from typing import Optional, List
from datetime import datetime
from django.shortcuts import get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
import logging

from accounts.models import Notification, CustomUser
from api.middleware import AuthMiddleware

logger = logging.getLogger(__name__)

# Create router for Notification endpoints
router = Router(tags=["notifications"])

# ============================================================================
# SCHEMAS
# ============================================================================

class NotificationIn(Schema):
    """Schema for creating a new notification"""
    user_id: int
    title: str
    body: str


class NotificationUpdate(Schema):
    """Schema for updating notification data"""
    title: Optional[str] = None
    body: Optional[str] = None
    is_read: Optional[bool] = None


class CustomUserOut(Schema):
    """Simplified user schema for notification responses"""
    id: int
    username: str
    phone: str


class NotificationOut(Schema):
    """Schema for notification output"""
    id: int
    user: CustomUserOut
    title: str
    body: str
    is_read: bool
    created_at: datetime
    updated_at: datetime


class PaginatedNotificationResponse(Schema):
    """Paginated response for notifications"""
    notifications: List[NotificationOut]
    total_count: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_previous: bool


class NotificationMarkReadRequest(Schema):
    """Schema for marking notifications as read"""
    notification_ids: List[int]


# ============================================================================
# CRUD ENDPOINTS
# ============================================================================

@router.get("/", response=PaginatedNotificationResponse, auth=AuthMiddleware)
def list_notifications(
    request,
    page: int = 1,
    page_size: int = 20,
    user_id: Optional[int] = None,
    is_read: Optional[bool] = None,
    search: Optional[str] = None,
) -> PaginatedNotificationResponse:
    """
    List all notifications with pagination and filtering.
    Requires authentication.
    """
    try:
        # Validate page_size
        if page_size > 100:
            page_size = 100
        elif page_size < 1:
            page_size = 20

        # Build queryset
        queryset = Notification.objects.filter(
            deleted_at__isnull=True
        ).select_related('user')

        # Apply filters
        if user_id:
            queryset = queryset.filter(user_id=user_id)

        if is_read is not None:
            queryset = queryset.filter(is_read=is_read)

        if search:
            queryset = queryset.filter(
                Q(title__icontains=search) | Q(body__icontains=search)
            )

        # Order by creation date (newest first)
        queryset = queryset.order_by('-created_at')

        # Paginate
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # Convert to response format
        notifications = []
        for notification in page_obj.object_list:
            notifications.append(NotificationOut(
                id=notification.id,
                user=CustomUserOut(
                    id=notification.user.id,
                    username=notification.user.username,
                    phone=notification.user.phone,
                ),
                title=notification.title,
                body=notification.body,
                is_read=notification.is_read,
                created_at=notification.created_at,
                updated_at=notification.updated_at,
            ))

        return PaginatedNotificationResponse(
            notifications=notifications,
            total_count=paginator.count,
            page=page,
            page_size=page_size,
            total_pages=paginator.num_pages,
            has_next=page_obj.has_next(),
            has_previous=page_obj.has_previous(),
        )

    except Exception as e:
        logger.exception(f"Error listing notifications: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/my", response=PaginatedNotificationResponse, auth=AuthMiddleware)
def list_my_notifications(
    request,
    page: int = 1,
    page_size: int = 20,
    is_read: Optional[bool] = None,
) -> PaginatedNotificationResponse:
    """
    List current user's notifications with pagination and filtering.
    Requires authentication.
    """
    try:
        # Validate page_size
        if page_size > 100:
            page_size = 100
        elif page_size < 1:
            page_size = 20

        # Build queryset for current user
        queryset = Notification.objects.filter(
            user=request.user,
            deleted_at__isnull=True
        )

        # Apply filters
        if is_read is not None:
            queryset = queryset.filter(is_read=is_read)

        # Order by creation date (newest first)
        queryset = queryset.order_by('-created_at')

        # Paginate
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # Convert to response format
        notifications = []
        for notification in page_obj.object_list:
            notifications.append(NotificationOut(
                id=notification.id,
                user=CustomUserOut(
                    id=notification.user.id,
                    username=notification.user.username,
                    phone=notification.user.phone,
                ),
                title=notification.title,
                body=notification.body,
                is_read=notification.is_read,
                created_at=notification.created_at,
                updated_at=notification.updated_at,
            ))

        return PaginatedNotificationResponse(
            notifications=notifications,
            total_count=paginator.count,
            page=page,
            page_size=page_size,
            total_pages=paginator.num_pages,
            has_next=page_obj.has_next(),
            has_previous=page_obj.has_previous(),
        )

    except Exception as e:
        logger.exception(f"Error listing user notifications: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/{notification_id}", response=NotificationOut, auth=AuthMiddleware)
def get_notification(request, notification_id: int) -> NotificationOut:
    """
    Get a specific notification by ID.
    Requires authentication.
    """
    try:
        notification = get_object_or_404(
            Notification.objects.select_related('user'),
            id=notification_id,
            deleted_at__isnull=True
        )

        return NotificationOut(
            id=notification.id,
            user=CustomUserOut(
                id=notification.user.id,
                username=notification.user.username,
                phone=notification.user.phone,
            ),
            title=notification.title,
            body=notification.body,
            is_read=notification.is_read,
            created_at=notification.created_at,
            updated_at=notification.updated_at,
        )

    except Exception as e:
        logger.exception(f"Error getting notification {notification_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.post("/", response=NotificationOut, auth=AuthMiddleware)
def create_notification(request, data: NotificationIn) -> NotificationOut:
    """
    Create a new notification.
    Requires authentication.
    """
    try:
        # Check if user exists
        user = get_object_or_404(
            CustomUser,
            id=data.user_id,
            deleted_at__isnull=True
        )

        # Create notification
        notification = Notification.objects.create(
            user=user,
            title=data.title,
            body=data.body,
            is_read=False,
        )

        return NotificationOut(
            id=notification.id,
            user=CustomUserOut(
                id=notification.user.id,
                username=notification.user.username,
                phone=notification.user.phone,
            ),
            title=notification.title,
            body=notification.body,
            is_read=notification.is_read,
            created_at=notification.created_at,
            updated_at=notification.updated_at,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error creating notification: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.put("/{notification_id}", response=NotificationOut, auth=AuthMiddleware)
def update_notification(request, notification_id: int, data: NotificationUpdate) -> NotificationOut:
    """
    Update a notification.
    Requires authentication.
    """
    try:
        notification = get_object_or_404(
            Notification.objects.select_related('user'),
            id=notification_id,
            deleted_at__isnull=True
        )

        # Update fields if provided
        if data.title is not None:
            notification.title = data.title

        if data.body is not None:
            notification.body = data.body

        if data.is_read is not None:
            notification.is_read = data.is_read

        notification.save()

        return NotificationOut(
            id=notification.id,
            user=CustomUserOut(
                id=notification.user.id,
                username=notification.user.username,
                phone=notification.user.phone,
            ),
            title=notification.title,
            body=notification.body,
            is_read=notification.is_read,
            created_at=notification.created_at,
            updated_at=notification.updated_at,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error updating notification {notification_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.post("/mark-read", auth=AuthMiddleware)
def mark_notifications_read(request, data: NotificationMarkReadRequest):
    """
    Mark multiple notifications as read.
    Requires authentication.
    """
    try:
        # Update notifications
        updated_count = Notification.objects.filter(
            id__in=data.notification_ids,
            deleted_at__isnull=True
        ).update(is_read=True)

        return {
            "message": f"Marked {updated_count} notifications as read",
            "updated_count": updated_count
        }

    except Exception as e:
        logger.exception(f"Error marking notifications as read: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.delete("/{notification_id}", auth=AuthMiddleware)
def delete_notification(request, notification_id: int):
    """
    Soft delete a notification.
    Requires authentication.
    """
    try:
        notification = get_object_or_404(
            Notification,
            id=notification_id,
            deleted_at__isnull=True
        )

        # Perform soft delete
        notification.delete()

        return {"message": "Notification deleted successfully"}

    except Exception as e:
        logger.exception(f"Error deleting notification {notification_id}: {str(e)}")
        raise HttpError(500, "Internal server error")
