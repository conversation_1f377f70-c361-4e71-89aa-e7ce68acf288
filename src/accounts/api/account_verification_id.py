"""
CRUD API endpoints for AccountVerificationId model.
"""

from ninja import Router, Schema
from ninja.errors import HttpError
from typing import Optional, List
from datetime import datetime
from django.shortcuts import get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
import logging

from accounts.models import AccountVerificationId, CustomUser
from api.middleware import AuthMiddleware

logger = logging.getLogger(__name__)

# Create router for AccountVerificationId endpoints
router = Router(tags=["account-verifications"])

# ============================================================================
# SCHEMAS
# ============================================================================


class AccountVerificationIdIn(Schema):
    """Schema for creating a new account verification"""

    user_id: int


class AccountVerificationIdUpdate(Schema):
    """Schema for updating account verification data"""

    verified: Optional[bool] = None


class CustomUserOut(Schema):
    """Simplified user schema for verification responses"""

    id: int
    username: str
    phone: str


class AccountVerificationIdOut(Schema):
    """Schema for account verification output"""

    id: int
    user: CustomUserOut
    front_image_url: Optional[str] = None
    back_image_url: Optional[str] = None
    verified: bool
    created_at: datetime
    updated_at: datetime


class PaginatedAccountVerificationResponse(Schema):
    """Paginated response for account verifications"""

    verifications: List[AccountVerificationIdOut]
    total_count: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_previous: bool


# ============================================================================
# CRUD ENDPOINTS
# ============================================================================


@router.get("/", response=PaginatedAccountVerificationResponse, auth=AuthMiddleware)
def list_account_verifications(
    request,
    page: int = 1,
    page_size: int = 20,
    user_id: Optional[int] = None,
    verified: Optional[bool] = None,
) -> PaginatedAccountVerificationResponse:
    """
    List all account verifications with pagination and filtering.
    Requires authentication.
    """
    try:
        # Validate page_size
        if page_size > 100:
            page_size = 100
        elif page_size < 1:
            page_size = 20

        # Build queryset
        queryset = AccountVerificationId.objects.filter(
            deleted_at__isnull=True
        ).select_related("user")

        # Apply filters
        if user_id:
            queryset = queryset.filter(user_id=user_id)

        if verified is not None:
            queryset = queryset.filter(verified=verified)

        # Order by creation date (newest first)
        queryset = queryset.order_by("-created_at")

        # Paginate
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # Convert to response format
        verifications = []
        for verification in page_obj.object_list:
            verifications.append(
                AccountVerificationIdOut(
                    id=verification.id,
                    user=CustomUserOut(
                        id=verification.user.id,
                        username=verification.user.username,
                        phone=verification.user.phone,
                    ),
                    front_image_url=verification.front_image.url
                    if verification.front_image
                    else None,
                    back_image_url=verification.back_image.url
                    if verification.back_image
                    else None,
                    verified=verification.verified,
                    created_at=verification.created_at,
                    updated_at=verification.updated_at,
                )
            )

        return PaginatedAccountVerificationResponse(
            verifications=verifications,
            total_count=paginator.count,
            page=page,
            page_size=page_size,
            total_pages=paginator.num_pages,
            has_next=page_obj.has_next(),
            has_previous=page_obj.has_previous(),
        )

    except Exception as e:
        logger.exception(f"Error listing account verifications: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get(
    "/{verification_id}", response=AccountVerificationIdOut, auth=AuthMiddleware
)
def get_account_verification(request, verification_id: int) -> AccountVerificationIdOut:
    """
    Get a specific account verification by ID.
    Requires authentication.
    """
    try:
        verification = get_object_or_404(
            AccountVerificationId.objects.select_related("user"),
            id=verification_id,
            deleted_at__isnull=True,
        )

        return AccountVerificationIdOut(
            id=verification.id,
            user=CustomUserOut(
                id=verification.user.id,
                username=verification.user.username,
                phone=verification.user.phone,
            ),
            front_image_url=verification.front_image.url
            if verification.front_image
            else None,
            back_image_url=verification.back_image.url
            if verification.back_image
            else None,
            verified=verification.verified,
            created_at=verification.created_at,
            updated_at=verification.updated_at,
        )

    except Exception as e:
        logger.exception(
            f"Error getting account verification {verification_id}: {str(e)}"
        )
        raise HttpError(500, "Internal server error")


@router.get(
    "/user/{user_id}", response=List[AccountVerificationIdOut], auth=AuthMiddleware
)
def get_user_account_verifications(
    request, user_id: int
) -> List[AccountVerificationIdOut]:
    """
    Get all account verifications for a specific user.
    Requires authentication.
    """
    try:
        verifications = (
            AccountVerificationId.objects.filter(
                user_id=user_id, deleted_at__isnull=True
            )
            .select_related("user")
            .order_by("-created_at")
        )

        result = []
        for verification in verifications:
            result.append(
                AccountVerificationIdOut(
                    id=verification.id,
                    user=CustomUserOut(
                        id=verification.user.id,
                        username=verification.user.username,
                        phone=verification.user.phone,
                    ),
                    front_image_url=verification.front_image.url
                    if verification.front_image
                    else None,
                    back_image_url=verification.back_image.url
                    if verification.back_image
                    else None,
                    verified=verification.verified,
                    created_at=verification.created_at,
                    updated_at=verification.updated_at,
                )
            )

        return result

    except Exception as e:
        logger.exception(
            f"Error getting account verifications for user {user_id}: {str(e)}"
        )
        raise HttpError(500, "Internal server error")


@router.post("/", response=AccountVerificationIdOut, auth=AuthMiddleware)
def create_account_verification(
    request, data: AccountVerificationIdIn
) -> AccountVerificationIdOut:
    """
    Create a new account verification.
    Requires authentication.
    """
    try:
        # Check if user exists
        user = get_object_or_404(CustomUser, id=data.user_id, deleted_at__isnull=True)

        # Create account verification
        verification = AccountVerificationId.objects.create(
            user=user,
            verified=False,
        )

        return AccountVerificationIdOut(
            id=verification.id,
            user=CustomUserOut(
                id=verification.user.id,
                username=verification.user.username,
                phone=verification.user.phone,
            ),
            front_image_url=verification.front_image.url
            if verification.front_image
            else None,
            back_image_url=verification.back_image.url
            if verification.back_image
            else None,
            verified=verification.verified,
            created_at=verification.created_at,
            updated_at=verification.updated_at,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error creating account verification: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.put(
    "/{verification_id}", response=AccountVerificationIdOut, auth=AuthMiddleware
)
def update_account_verification(
    request, verification_id: int, data: AccountVerificationIdUpdate
) -> AccountVerificationIdOut:
    """
    Update an account verification.
    Requires authentication.
    """
    try:
        verification = get_object_or_404(
            AccountVerificationId.objects.select_related("user"),
            id=verification_id,
            deleted_at__isnull=True,
        )

        # Update fields if provided
        if data.verified is not None:
            verification.verified = data.verified

        verification.save()

        return AccountVerificationIdOut(
            id=verification.id,
            user=CustomUserOut(
                id=verification.user.id,
                username=verification.user.username,
                phone=verification.user.phone,
            ),
            front_image_url=verification.front_image.url
            if verification.front_image
            else None,
            back_image_url=verification.back_image.url
            if verification.back_image
            else None,
            verified=verification.verified,
            created_at=verification.created_at,
            updated_at=verification.updated_at,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(
            f"Error updating account verification {verification_id}: {str(e)}"
        )
        raise HttpError(500, "Internal server error")


@router.delete("/{verification_id}", auth=AuthMiddleware)
def delete_account_verification(request, verification_id: int):
    """
    Soft delete an account verification.
    Requires authentication.
    """
    try:
        verification = get_object_or_404(
            AccountVerificationId, id=verification_id, deleted_at__isnull=True
        )

        # Perform soft delete
        verification.delete()

        return {"message": "Account verification deleted successfully"}

    except Exception as e:
        logger.exception(
            f"Error deleting account verification {verification_id}: {str(e)}"
        )
        raise HttpError(500, "Internal server error")
