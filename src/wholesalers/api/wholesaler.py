"""
CRUD API endpoints for Wholesaler model.
"""

from ninja import Router, Schema
from ninja.errors import HttpError
from typing import Optional, List
from datetime import datetime
from django.shortcuts import get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
import logging

from wholesalers.models import Wholesaler
from accounts.models import CustomUser
from api.middleware import AuthMiddleware

logger = logging.getLogger(__name__)

# Create router for Wholesaler endpoints
router = Router(tags=["wholesalers"])

# ============================================================================
# SCHEMAS
# ============================================================================


class WholesalerIn(Schema):
    """Schema for creating a new wholesaler"""

    title: str
    description: str = ""
    phone: str
    address: str
    user_id: int


class WholesalerUpdate(Schema):
    """Schema for updating wholesaler data"""

    title: Optional[str] = None
    description: Optional[str] = None
    phone: Optional[str] = None
    address: Optional[str] = None


class CustomUserOut(Schema):
    """Simplified user schema for wholesaler responses"""

    id: int
    username: str
    phone: str


class WholesalerOut(Schema):
    """Schema for wholesaler output"""

    id: int
    title: str
    description: str
    phone: str
    address: str
    user: CustomUserOut
    created_at: datetime
    updated_at: datetime


class PaginatedWholesalerResponse(Schema):
    """Paginated response for wholesalers"""

    wholesalers: List[WholesalerOut]
    total_count: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_previous: bool


# ============================================================================
# HELPER FUNCTIONS
# ============================================================================


def _build_wholesaler_response(wholesaler) -> WholesalerOut:
    """Helper function to build WholesalerOut response"""
    return WholesalerOut(
        id=wholesaler.id,
        title=wholesaler.title,
        description=wholesaler.description,
        phone=wholesaler.phone,
        address=wholesaler.address,
        user=CustomUserOut(
            id=wholesaler.user.id,
            username=wholesaler.user.username,
            phone=wholesaler.user.phone,
        ),
        created_at=wholesaler.created_at,
        updated_at=wholesaler.updated_at,
    )


# ============================================================================
# CRUD ENDPOINTS
# ============================================================================


@router.get("/", response=PaginatedWholesalerResponse, auth=AuthMiddleware)
def list_wholesalers(
    request,
    page: int = 1,
    page_size: int = 20,
    search: Optional[str] = None,
    user_id: Optional[int] = None,
) -> PaginatedWholesalerResponse:
    """
    List all wholesalers with pagination and filtering.
    Requires authentication.
    """
    try:
        # Validate page_size
        if page_size > 100:
            page_size = 100
        elif page_size < 1:
            page_size = 20

        # Build queryset
        queryset = Wholesaler.objects.filter(deleted_at__isnull=True).select_related(
            "user"
        )

        # Apply filters
        if search:
            queryset = queryset.filter(
                Q(title__icontains=search)
                | Q(description__icontains=search)
                | Q(phone__icontains=search)
                | Q(address__icontains=search)
            )

        if user_id:
            queryset = queryset.filter(user_id=user_id)

        # Order by creation date (newest first)
        queryset = queryset.order_by("-created_at")

        # Paginate
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # Convert to response format
        wholesalers = [
            _build_wholesaler_response(wholesaler)
            for wholesaler in page_obj.object_list
        ]

        return PaginatedWholesalerResponse(
            wholesalers=wholesalers,
            total_count=paginator.count,
            page=page,
            page_size=page_size,
            total_pages=paginator.num_pages,
            has_next=page_obj.has_next(),
            has_previous=page_obj.has_previous(),
        )

    except Exception as e:
        logger.exception(f"Error listing wholesalers: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/{wholesaler_id}", response=WholesalerOut, auth=AuthMiddleware)
def get_wholesaler(request, wholesaler_id: int) -> WholesalerOut:
    """
    Get a specific wholesaler by ID.
    Requires authentication.
    """
    try:
        wholesaler = get_object_or_404(
            Wholesaler.objects.select_related("user"),
            id=wholesaler_id,
            deleted_at__isnull=True,
        )

        return _build_wholesaler_response(wholesaler)

    except Exception as e:
        logger.exception(f"Error getting wholesaler {wholesaler_id}: {str(e)}")
        raise HttpError(500, "Internal server error")
